import os
import re
import json
from crewai.tools import BaseTool
from typing import Type, Optional, Dict, Any, List
from pydantic import BaseModel, Field
try:
    from langdetect import detect, LangDetectError
except ImportError:
    def detect(text):
        return "en"
    class LangDetectError(Exception):
        pass


class ComplianceCheckerInput(BaseModel):
    """Input schema for ComplianceCheckerTool."""
    message_content: str = Field(..., description="The message content to check for compliance")
    recipient_user_id: Optional[str] = Field(None, description="Optional recipient user ID for personalization checks")
    message_type: Optional[str] = Field("outreach", description="Type of message: outreach, follow_up, or promotional")
    auto_send_threshold: Optional[float] = Field(0.75, description="Confidence threshold for auto-send approval")


class ComplianceCheckerTool(BaseTool):
    name: str = "MQL5 Compliance and Spam Checker"
    description: str = (
        "Comprehensive compliance checker for MQL5 private messages. Validates messages against "
        "MQL5 terms of service, spam policies, and community guidelines. Provides spam scoring, "
        "policy violation detection, and improvement suggestions. Use this tool before sending "
        "any outreach messages to ensure compliance and maximize delivery success."
    )
    args_schema: Type[BaseModel] = ComplianceCheckerInput

    def __init__(self):
        super().__init__()
        # Load compliance rules and thresholds
        self.max_message_length = int(os.environ.get('MAX_MESSAGE_LENGTH', '750'))
        self.auto_send_threshold = float(os.environ.get('AUTO_SEND_CONFIDENCE_THRESHOLD', '0.75'))

        # Spam detection patterns
        self.spam_patterns = {
            'excessive_caps': r'[A-Z]{5,}',
            'excessive_exclamation': r'!{3,}',
            'excessive_money_symbols': r'[\$€£¥]{2,}',
            'phone_numbers': r'(\+?\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}',
            'email_addresses': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            'urls': r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+',
            'money_promises': r'\b(guaranteed|promise|earn|profit|money|income|cash)\b.*\b(\$|\d+%|\d+\s*(dollars?|euros?|pounds?))\b',
            'urgency_words': r'\b(urgent|hurry|limited|expires?|deadline|act now|don\'t wait)\b'
        }

        # Prohibited content
        self.prohibited_terms = [
            'guaranteed profit', 'risk-free', 'get rich quick', 'easy money',
            'no risk', '100% profit', 'guaranteed returns', 'instant wealth',
            'secret system', 'insider information', 'sure thing', 'can\'t lose'
        ]

        # Professional language requirements
        self.required_elements = {
            'greeting': r'\b(hello|hi|dear|greetings)\b',
            'introduction': r'\b(i am|my name|i\'m|i work)\b',
            'value_proposition': r'\b(help|assist|benefit|improve|solution)\b',
            'call_to_action': r'\b(interested|learn more|discuss|contact|reply)\b'
        }

    def _run(self, message_content: str, recipient_user_id: Optional[str] = None,
             message_type: Optional[str] = "outreach",
             auto_send_threshold: Optional[float] = 0.75) -> str:
        """
        Check message compliance with MQL5 guidelines.

        Args:
            message_content: The message content to check
            recipient_user_id: Optional recipient user ID
            message_type: Type of message (outreach, follow_up, promotional)
            auto_send_threshold: Confidence threshold for auto-send

        Returns:
            Detailed compliance report with recommendations
        """
        try:
            report = f"Compliance Check Report:\n"
            report += "=" * 50 + "\n\n"

            # Basic message info
            report += f"📝 MESSAGE ANALYSIS:\n"
            report += f"  Length: {len(message_content)} characters (max: {self.max_message_length})\n"
            report += f"  Type: {message_type}\n"
            report += f"  Auto-send threshold: {auto_send_threshold}\n\n"

            # Language detection
            language = self._detect_language(message_content)
            report += f"  Language: {language}\n\n"

            # Compliance checks
            compliance_score = 0.0
            violations = []
            warnings = []
            suggestions = []

            # 1. Length check
            length_score, length_issues = self._check_length(message_content)
            compliance_score += length_score * 0.1
            if length_issues:
                violations.extend(length_issues)

            # 2. Spam pattern detection
            spam_score, spam_issues = self._check_spam_patterns(message_content)
            compliance_score += spam_score * 0.3
            if spam_issues:
                violations.extend(spam_issues)

            # 3. Prohibited content check
            content_score, content_issues = self._check_prohibited_content(message_content)
            compliance_score += content_score * 0.25
            if content_issues:
                violations.extend(content_issues)

            # 4. Professional structure check
            structure_score, structure_issues = self._check_message_structure(message_content)
            compliance_score += structure_score * 0.2
            if structure_issues:
                warnings.extend(structure_issues)

            # 5. Personalization check
            personal_score, personal_issues = self._check_personalization(message_content, recipient_user_id)
            compliance_score += personal_score * 0.15
            if personal_issues:
                suggestions.extend(personal_issues)

            # Generate compliance report
            report += f"🎯 COMPLIANCE SCORE: {compliance_score:.2f}/1.0\n\n"

            # Auto-send recommendation
            auto_send_approved = compliance_score >= auto_send_threshold
            report += f"🚦 AUTO-SEND STATUS: {'✅ APPROVED' if auto_send_approved else '❌ REQUIRES REVIEW'}\n\n"

            # Violations (critical issues)
            if violations:
                report += f"🚨 POLICY VIOLATIONS ({len(violations)}):\n"
                for i, violation in enumerate(violations, 1):
                    report += f"  {i}. {violation}\n"
                report += "\n"

            # Warnings (best practice issues)
            if warnings:
                report += f"⚠️ WARNINGS ({len(warnings)}):\n"
                for i, warning in enumerate(warnings, 1):
                    report += f"  {i}. {warning}\n"
                report += "\n"

            # Suggestions (improvements)
            if suggestions:
                report += f"💡 SUGGESTIONS ({len(suggestions)}):\n"
                for i, suggestion in enumerate(suggestions, 1):
                    report += f"  {i}. {suggestion}\n"
                report += "\n"

            # Improvement recommendations
            if compliance_score < auto_send_threshold:
                improvements = self._generate_improvements(violations, warnings, suggestions)
                report += f"🔧 IMPROVEMENT RECOMMENDATIONS:\n"
                report += improvements

            # Final recommendation
            report += f"📋 FINAL RECOMMENDATION:\n"
            if auto_send_approved:
                report += "  ✅ Message meets compliance standards and can be sent automatically.\n"
            elif compliance_score >= 0.6:
                report += "  ⚠️ Message needs minor adjustments before sending.\n"
            else:
                report += "  ❌ Message requires significant revision before sending.\n"

            return report.strip()

        except Exception as e:
            return f"Error checking compliance: {str(e)}"

    def _detect_language(self, text: str) -> str:
        """Detect the language of the message."""
        try:
            return detect(text)
        except LangDetectError:
            return "unknown"

    def _check_length(self, message: str) -> tuple:
        """Check message length compliance."""
        length = len(message)
        issues = []

        if length > self.max_message_length:
            issues.append(f"Message exceeds maximum length ({length}/{self.max_message_length} characters)")
            return 0.0, issues
        elif length < 50:
            issues.append("Message is too short and may appear unprofessional")
            return 0.5, issues

        return 1.0, issues

    def _check_spam_patterns(self, message: str) -> tuple:
        """Check for spam patterns in the message."""
        issues = []
        score = 1.0

        for pattern_name, pattern in self.spam_patterns.items():
            matches = re.findall(pattern, message, re.IGNORECASE)
            if matches:
                if pattern_name == 'excessive_caps':
                    issues.append(f"Excessive capital letters detected: {matches}")
                    score -= 0.2
                elif pattern_name == 'excessive_exclamation':
                    issues.append(f"Too many exclamation marks: {matches}")
                    score -= 0.15
                elif pattern_name == 'phone_numbers':
                    issues.append("Phone numbers not allowed in initial outreach")
                    score -= 0.3
                elif pattern_name == 'email_addresses':
                    issues.append("Email addresses not allowed in messages")
                    score -= 0.3
                elif pattern_name == 'urls':
                    issues.append("URLs not allowed in initial outreach")
                    score -= 0.4
                elif pattern_name == 'money_promises':
                    issues.append("Suspicious money-related promises detected")
                    score -= 0.5
                elif pattern_name == 'urgency_words':
                    issues.append("High-pressure urgency language detected")
                    score -= 0.2

        return max(score, 0.0), issues

    def _check_prohibited_content(self, message: str) -> tuple:
        """Check for prohibited content."""
        issues = []
        score = 1.0
        message_lower = message.lower()

        for term in self.prohibited_terms:
            if term in message_lower:
                issues.append(f"Prohibited term detected: '{term}'")
                score -= 0.3

        # Check for financial promises
        financial_red_flags = [
            r'\b\d+%\s*(profit|return|gain)\b',
            r'\b(no|zero)\s*risk\b',
            r'\bmake\s*\$\d+\b',
            r'\bguaranteed\s*(profit|money|income)\b'
        ]

        for pattern in financial_red_flags:
            if re.search(pattern, message_lower):
                issues.append("Unrealistic financial promises detected")
                score -= 0.4
                break

        return max(score, 0.0), issues

    def _check_message_structure(self, message: str) -> tuple:
        """Check professional message structure."""
        issues = []
        score = 1.0
        message_lower = message.lower()

        missing_elements = []
        for element, pattern in self.required_elements.items():
            if not re.search(pattern, message_lower):
                missing_elements.append(element)

        if missing_elements:
            issues.append(f"Missing professional elements: {', '.join(missing_elements)}")
            score -= 0.2 * len(missing_elements)

        # Check for proper sentence structure
        sentences = message.split('.')
        if len(sentences) < 2:
            issues.append("Message should contain multiple sentences for better readability")
            score -= 0.1

        # Check for questions (engagement)
        if '?' not in message:
            issues.append("Consider adding a question to encourage response")
            score -= 0.1

        return max(score, 0.0), issues

    def _check_personalization(self, message: str, user_id: Optional[str]) -> tuple:
        """Check message personalization."""
        suggestions = []
        score = 1.0

        # Generic greeting check
        generic_greetings = ['dear sir', 'dear madam', 'to whom it may concern', 'hello there']
        message_lower = message.lower()

        for greeting in generic_greetings:
            if greeting in message_lower:
                suggestions.append("Use more personalized greeting")
                score -= 0.2
                break

        # Template language detection
        template_phrases = [
            'this is a template', 'copy and paste', 'mass message',
            'sent to multiple', 'bulk message'
        ]

        for phrase in template_phrases:
            if phrase in message_lower:
                suggestions.append("Remove template-like language")
                score -= 0.3

        # Personalization elements
        personal_elements = ['your review', 'your experience', 'your trading', 'your feedback']
        has_personalization = any(element in message_lower for element in personal_elements)

        if not has_personalization:
            suggestions.append("Add specific references to user's activity or reviews")
            score -= 0.2

        return max(score, 0.0), suggestions

    def _generate_improvements(self, violations: List[str], warnings: List[str], suggestions: List[str]) -> str:
        """Generate specific improvement recommendations."""
        improvements = ""

        if violations:
            improvements += "Critical fixes needed:\n"
            for violation in violations:
                if "exceeds maximum length" in violation:
                    improvements += "  - Shorten message by removing unnecessary details\n"
                elif "Phone numbers" in violation:
                    improvements += "  - Remove phone number and use MQL5 messaging only\n"
                elif "URLs" in violation:
                    improvements += "  - Remove external links from initial message\n"
                elif "Prohibited term" in violation:
                    improvements += "  - Replace prohibited terms with compliant language\n"
                elif "financial promises" in violation:
                    improvements += "  - Remove unrealistic profit claims and guarantees\n"
            improvements += "\n"

        if warnings:
            improvements += "Professional improvements:\n"
            for warning in warnings:
                if "Missing professional elements" in warning:
                    improvements += "  - Add proper greeting, introduction, and call-to-action\n"
                elif "multiple sentences" in warning:
                    improvements += "  - Break content into clear, readable sentences\n"
                elif "question" in warning:
                    improvements += "  - Add engaging question to encourage response\n"
            improvements += "\n"

        if suggestions:
            improvements += "Enhancement opportunities:\n"
            for suggestion in suggestions:
                if "personalized greeting" in suggestion:
                    improvements += "  - Use recipient's name or specific reference\n"
                elif "template-like" in suggestion:
                    improvements += "  - Make language more conversational and natural\n"
                elif "specific references" in suggestion:
                    improvements += "  - Reference specific reviews or trading activity\n"

        return improvements
