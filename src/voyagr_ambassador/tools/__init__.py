"""
Voyagr Ambassador Tools Package

This package contains all the specialized tools used by the AI agents in the
Voyagr outreach pipeline for MQL5 Expert Advisor sellers.

Tool Categories:
- Data Retrieval: ZEP Graph integration and data access
- Analysis: Sentiment, similarity, and content analysis
- Compliance: Message validation and policy checking
- Localization: Language detection and cultural adaptation
- Product: Similarity matching and recommendation engine
"""

from .zep_tool import ZepKnowledgeGraphTool
from .review_analyzer_tool import ReviewDataExtractionTool
from .compliance_checker_tool import Comp<PERSON><PERSON>heckerTool

# Tool registry for easy access by agents
TOOL_REGISTRY = {
    # Data Retrieval Tools
    'zep_graph': ZepKnowledgeGraphTool,
    'review_data_extraction': ReviewDataExtractionTool,
    # Compliance Tools
    'compliance_checker': ComplianceCheckerTool,
}

# Agent-specific tool mappings
AGENT_TOOLS = {
    'data_retriever': [
        'zep_graph',
        'review_data_extraction'
    ],
    'user_profiler': [
        'zep_graph'
    ],
    'product_analyst': [
        'zep_graph'
    ],
    'strategy_designer': [
        'zep_graph'
    ],
    'copy_writer': [
        'zep_graph'
    ]
}


def get_tools_for_agent(agent_name: str) -> list:
    """
    Get the list of tool instances for a specific agent.

    Args:
        agent_name: Name of the agent ('data_retriever', 'user_profiler', etc.)

    Returns:
        List of instantiated tool objects for the agent
    """
    if agent_name not in AGENT_TOOLS:
        raise ValueError(f"Unknown agent: {agent_name}. Available agents: {list(AGENT_TOOLS.keys())}")

    tool_names = AGENT_TOOLS[agent_name]
    tools = []

    for tool_name in tool_names:
        if tool_name in TOOL_REGISTRY:
            try:
                tool_class = TOOL_REGISTRY[tool_name]
                tool_instance = tool_class()
                tools.append(tool_instance)
            except Exception as e:
                print(f"Warning: Failed to instantiate tool {tool_name}: {e}")
        else:
            print(f"Warning: Tool {tool_name} not found in registry")

    return tools


__all__ = [
    # Tool classes
    'ZepKnowledgeGraphTool',
    'ReviewDataExtractionTool',

    # Registry and mappings
    'TOOL_REGISTRY',
    'AGENT_TOOLS',

    # Utility functions
    'get_tools_for_agent'
]
