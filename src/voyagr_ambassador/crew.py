from crewai import Agent, Crew, Process, Task
from crewai.project import CrewB<PERSON>, agent, crew, task
from .tools import get_tools_for_agent
from .models.agent_outputs import (
    DataRetrieverOutput,
    UserProfilerOutput,
    ProductAnalystOutput,
    StrategyDesignerOutput,
    CopyWriterOutput
)

@CrewBase
class voyagr_ambassadorCrew():
    """AI-Driven Outreach Pipeline Crew"""

    @agent
    def data_retriever(self) -> Agent:
        return Agent(
            config=self.agents_config['Data-Retriever'],
            tools=get_tools_for_agent('data_retriever'),
        )

    @agent
    def user_profiler(self) -> Agent:
        return Agent(
            config=self.agents_config['User-Profiler'],
            tools=get_tools_for_agent('user_profiler'),
        )

    @agent
    def product_analyst(self) -> Agent:
        return Agent(
            config=self.agents_config['Product-Analyst'],
            tools=get_tools_for_agent('product_analyst'),
        )

    @agent
    def strategy_designer(self) -> Agent:
        return Agent(
            config=self.agents_config['Strategy-Designer'],
            tools=get_tools_for_agent('strategy_designer'),
        )

    @agent
    def copy_writer(self) -> Agent:
        return Agent(
            config=self.agents_config['Copy-Writer'],
            tools=get_tools_for_agent('copy_writer'),
        )


    @task
    def retrieve_data_task(self) -> Task:
        return Task(
            config=self.tasks_config['retrieve_data_task'],
            output_json=DataRetrieverOutput,
        )

    @task
    def enrich_user_profile_task(self) -> Task:
        return Task(
            config=self.tasks_config['enrich_user_profile_task'],
            output_json=UserProfilerOutput,
        )

    @task
    def analyze_product_task(self) -> Task:
        return Task(
            config=self.tasks_config['analyze_product_task'],
            output_json=ProductAnalystOutput,
        )

    @task
    def design_conversation_strategy_task(self) -> Task:
        return Task(
            config=self.tasks_config['design_conversation_strategy_task'],
            output_json=StrategyDesignerOutput,
        )

    @task
    def create_message_task(self) -> Task:
        return Task(
            config=self.tasks_config['create_message_task'],
            output_json=CopyWriterOutput,
        )

    @crew
    def crew(self) -> Crew:
        return Crew(
            agents=self.agents['data_retriever'],
            tasks=self.tasks,
            process=Process.sequential,
            verbose=True,
        )
