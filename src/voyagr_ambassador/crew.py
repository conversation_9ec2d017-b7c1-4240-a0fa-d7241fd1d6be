from crewai import Agent, Crew, Process, Task
from crewai.project import CrewBase, agent, crew, task

# Placeholder imports — you will swap these with real ones
# from src.tools import ZepGraphTool, ComplianceCheckerTool, etc.

@CrewBase
class voyagr_ambassadorCrew():
    """AI-Driven Outreach Pipeline Crew"""

    @agent
    def data_retriever(self) -> Agent:
        return Agent(
            config=self.agents_config['Data-Retriever'],
            tools=[],  # Add ZepGraphTool here
        )

    @agent
    def user_profiler(self) -> Agent:
        return Agent(
            config=self.agents_config['User-Profiler'],
            tools=[],  # Add LanguageDetectorTool here
        )

    @agent
    def product_analyst(self) -> Agent:
        return Agent(
            config=self.agents_config['Product-Analyst'],
            tools=[],  # Add EmbeddingSimilarityTool here
        )

    @agent
    def strategy_designer(self) -> Agent:
        return Agent(
            config=self.agents_config['Strategy-Designer'],
            tools=[],
        )

    @agent
    def copy_writer(self) -> Agent:
        return Agent(
            config=self.agents_config['Copy-Writer'],
            tools=[],  # Add TranslationTool if needed
        )


    @task
    def retrieve_data_task(self) -> Task:
        return Task(
            config=self.tasks_config['retrieve_data_task'],
            tools=[],
        )

    @task
    def enrich_user_profile_task(self) -> Task:
        return Task(
            config=self.tasks_config['enrich_user_profile_task'],
            tools=[],
        )

    @task
    def analyze_product_task(self) -> Task:
        return Task(
            config=self.tasks_config['analyze_product_task'],
            tools=[],
        )

    @task
    def design_conversation_strategy_task(self) -> Task:
        return Task(
            config=self.tasks_config['design_conversation_strategy_task'],
            tools=[],
        )

    @task
    def create_message_task(self) -> Task:
        return Task(
            config=self.tasks_config['create_message_task'],
            tools=[],
        )

    @crew
    def crew(self) -> Crew:
        return Crew(
            agents=self.agents,
            tasks=self.tasks,
            process=Process.sequential,
            verbose=True,
        )
