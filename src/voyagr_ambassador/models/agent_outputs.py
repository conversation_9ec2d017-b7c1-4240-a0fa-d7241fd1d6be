"""
Structured output models for each agent in the Voyagr outreach pipeline.
These Pydantic models define the expected output format for each agent.
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class SentimentLabel(str, Enum):
    """Sentiment classification labels."""
    VERY_POSITIVE = "very_positive"
    POSITIVE = "positive"
    NEUTRAL = "neutral"
    NEGATIVE = "negative"
    VERY_NEGATIVE = "very_negative"


class RiskLevel(str, Enum):
    """Risk level classifications."""
    CONSERVATIVE = "conservative"
    MODERATE = "moderate"
    AGGRESSIVE = "aggressive"
    UNKNOWN = "unknown"


class ExperienceLevel(str, Enum):
    """Trading experience levels."""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"
    UNKNOWN = "unknown"


class ConversationIntent(str, Enum):
    """High-level conversation intents."""
    REMEDY = "remedy"  # Address negative experience
    CROSS_SELL = "cross_sell"  # Recommend similar/better products
    CASE_STUDY = "case_study"  # Leverage positive experience
    EDUCATION = "education"  # Provide value through education
    FOLLOW_UP = "follow_up"  # Continue existing conversation


class MessageTone(str, Enum):
    """Message tone options."""
    EMPATHETIC = "empathetic"
    ENTHUSIASTIC = "enthusiastic"
    PROFESSIONAL = "professional"
    EDUCATIONAL = "educational"
    FRIENDLY = "friendly"


# Data Retriever Output
class ReviewData(BaseModel):
    """Review information from ZEP graph."""
    review_id: str
    product_id: str
    product_title: str
    rating: str
    review_text: str
    date: Optional[str] = None
    category: Optional[str] = None
    verified: Optional[bool] = None


class UserData(BaseModel):
    """User information from ZEP graph."""
    user_id: str
    username: Optional[str] = None
    registration_date: Optional[str] = None
    country: Optional[str] = None
    language: Optional[str] = None
    total_reviews: Optional[int] = None
    average_rating_given: Optional[float] = None
    last_activity: Optional[str] = None


class ProductData(BaseModel):
    """Product information from ZEP graph."""
    product_id: str
    title: str
    category: str
    product_type: Optional[str] = None
    price: Optional[float] = None
    rating: Optional[float] = None
    total_reviews: Optional[int] = None
    features: Optional[List[str]] = None
    description: Optional[str] = None


class DataRetrieverOutput(BaseModel):
    """Structured output from Data-Retriever agent."""
    review: ReviewData
    user: UserData
    product: ProductData
    similar_products: List[ProductData] = Field(default_factory=list)
    user_review_history: List[ReviewData] = Field(default_factory=list)
    retrieval_timestamp: str
    data_completeness_score: float = Field(ge=0.0, le=1.0)


# User Profiler Output
class TradingProfile(BaseModel):
    """Trading-specific profile information."""
    experience_level: ExperienceLevel
    risk_tolerance: RiskLevel
    preferred_strategies: List[str] = Field(default_factory=list)
    trading_frequency: Optional[str] = None
    account_size_category: Optional[str] = None


class CommunicationProfile(BaseModel):
    """Communication preferences and patterns."""
    preferred_language: str
    formality_level: str  # formal, semi_formal, casual
    response_pattern: Optional[str] = None  # quick, delayed, selective
    engagement_level: str  # high, medium, low


class UserProfilerOutput(BaseModel):
    """Structured output from User-Profiler agent."""
    user_id: str
    trading_profile: TradingProfile
    communication_profile: CommunicationProfile
    sentiment_towards_products: SentimentLabel
    reputation_score: float = Field(ge=0.0, le=1.0)
    activity_level: str  # active, moderate, inactive
    key_interests: List[str] = Field(default_factory=list)
    profile_summary: str = Field(max_length=200)  # Concise 200-char summary
    confidence_score: float = Field(ge=0.0, le=1.0)


# Product Analyst Output
class ProductStrengths(BaseModel):
    """Product strengths analysis."""
    performance_indicators: List[str] = Field(default_factory=list)
    user_satisfaction_points: List[str] = Field(default_factory=list)
    competitive_advantages: List[str] = Field(default_factory=list)


class ProductWeaknesses(BaseModel):
    """Product weaknesses analysis."""
    performance_issues: List[str] = Field(default_factory=list)
    user_complaints: List[str] = Field(default_factory=list)
    competitive_disadvantages: List[str] = Field(default_factory=list)


class FitSignals(BaseModel):
    """User-product fit signals."""
    positive_signals: List[str] = Field(default_factory=list)
    negative_signals: List[str] = Field(default_factory=list)
    neutral_signals: List[str] = Field(default_factory=list)


class ProductRecommendation(BaseModel):
    """Product recommendation with reasoning."""
    product_id: str
    product_title: str
    similarity_score: float = Field(ge=0.0, le=1.0)
    recommendation_reason: str
    fit_score: float = Field(ge=0.0, le=1.0)


class ProductAnalystOutput(BaseModel):
    """Structured output from Product-Analyst agent."""
    reviewed_product_id: str
    strengths: ProductStrengths
    weaknesses: ProductWeaknesses
    fit_signals: FitSignals
    market_position: str  # leader, challenger, niche, emerging
    user_sentiment_analysis: SentimentLabel
    recommendations: List[ProductRecommendation] = Field(default_factory=list)
    analysis_confidence: float = Field(ge=0.0, le=1.0)


# Strategy Designer Output
class ConversationStrategy(BaseModel):
    """High-level conversation strategy."""
    primary_intent: ConversationIntent
    secondary_intents: List[ConversationIntent] = Field(default_factory=list)
    approach_rationale: str
    key_talking_points: List[str] = Field(default_factory=list)
    avoid_topics: List[str] = Field(default_factory=list)


class MessageSequence(BaseModel):
    """Planned message sequence."""
    ice_breaker_focus: str
    follow_up_strategy: str
    call_to_action_type: str
    escalation_path: Optional[str] = None


class GuardRails(BaseModel):
    """Communication guard rails."""
    tone_requirements: MessageTone
    formality_level: str
    cultural_considerations: List[str] = Field(default_factory=list)
    compliance_notes: List[str] = Field(default_factory=list)


class StrategyDesignerOutput(BaseModel):
    """Structured output from Strategy-Designer agent."""
    user_id: str
    product_id: str
    conversation_strategy: ConversationStrategy
    message_sequence: MessageSequence
    guard_rails: GuardRails
    personalization_elements: List[str] = Field(default_factory=list)
    expected_outcome: str
    strategy_confidence: float = Field(ge=0.0, le=1.0)


# Copy Writer Output
class MessageContent(BaseModel):
    """Individual message content."""
    message_type: str  # ice_breaker, follow_up, call_to_action
    subject_line: Optional[str] = None
    message_body: str
    character_count: int
    language: str
    formality_level: str


class LocalizationNotes(BaseModel):
    """Localization and cultural notes."""
    target_language: str
    cultural_adaptations: List[str] = Field(default_factory=list)
    translation_notes: List[str] = Field(default_factory=list)


class CopyWriterOutput(BaseModel):
    """Structured output from Copy-Writer agent."""
    user_id: str
    message_sequence: List[MessageContent]
    localization: LocalizationNotes
    personalization_applied: List[str] = Field(default_factory=list)
    style_guide_compliance: bool
    estimated_engagement_score: float = Field(ge=0.0, le=1.0)
    alternative_versions: List[MessageContent] = Field(default_factory=list)

# Pipeline Output (Final Result)
class OutreachPipelineOutput(BaseModel):
    """Complete pipeline output."""
    pipeline_id: str
    user_id: str
    review_id: str
    product_id: str

    # Agent outputs
    data_retrieval: DataRetrieverOutput
    user_profile: UserProfilerOutput
    product_analysis: ProductAnalystOutput
    strategy: StrategyDesignerOutput
    copy_output: CopyWriterOutput

    # Pipeline metadata
    pipeline_version: str
    execution_timestamp: str
    total_processing_time: float
    success: bool
    error_messages: List[str] = Field(default_factory=list)
