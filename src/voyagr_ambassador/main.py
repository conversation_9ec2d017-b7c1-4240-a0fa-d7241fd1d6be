#!/usr/bin/env python
import sys
from dotenv import load_dotenv
from .crew import voyagr_ambassadorCrew

# Load environment variables from .env file
load_dotenv()

# This main file is intended to be a way for your to run your
# crew locally, so refrain from adding unnecessary logic into this file.
# Replace with inputs you want to test with, it will automatically
# interpolate any tasks and agents information


def run_with_inputs(review_id: str, user_id: str = None, product_id: str = None):
    """
    Run the crew with specific inputs.

    Args:
        review_id: The review ID to process
        user_id: Optional user ID (will be extracted from review if not provided)
        product_id: Optional product ID (will be extracted from review if not provided)
    """
    inputs = {
        'review_id': review_id,
        'user_id': user_id,
        'product_id': product_id,
    }
    return voyagr_ambassadorCrew().crew().kickoff(inputs=inputs)

def run():
    """
    Run the crew with sample inputs for testing.
    """
    inputs = {
        'review_id': 'sample_review_123',  # Replace with actual review ID
        'user_id': 'sample_user_456',      # Replace with actual user ID
        'product_id': 'sample_ea_789',     # Replace with actual EA product ID
    }
    voyagr_ambassadorCrew().crew().kickoff(inputs=inputs)


def train():
    """
    Train the crew for a given number of iterations.
    """
    inputs = {
        'review_id': 'training_review_123',
        'user_id': 'training_user_456',
        'product_id': 'training_ea_789',
    }
    try:
        voyagr_ambassadorCrew().crew().train(n_iterations=int(sys.argv[1]), filename=sys.argv[2], inputs=inputs)

    except Exception as e:
        raise Exception(f"An error occurred while training the crew: {e}")

def replay():
    """
    Replay the crew execution from a specific task.
    """
    try:
        voyagr_ambassadorCrew().crew().replay(task_id=sys.argv[1])

    except Exception as e:
        raise Exception(f"An error occurred while replaying the crew: {e}")

def test():
    """
    Test the crew execution and returns the results.
    """
    inputs = {
        'review_id': 'test_review_123',
        'user_id': 'test_user_456',
        'product_id': 'test_ea_789',
    }
    try:
        voyagr_ambassadorCrew().crew().test(n_iterations=int(sys.argv[1]), openai_model_name=sys.argv[2], inputs=inputs)

    except Exception as e:
        raise Exception(f"An error occurred while testing the crew: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: main.py <command> [<args>]")
        print("Commands:")
        print("  run                    - Run with sample data")
        print("  run_custom <review_id> [<user_id>] [<product_id>] - Run with custom inputs")
        print("  train <iterations> <filename> - Train the crew")
        print("  replay <task_id>       - Replay from specific task")
        print("  test <iterations> <model> - Test the crew")
        sys.exit(1)

    command = sys.argv[1]
    if command == "run":
        run()
    elif command == "run_custom":
        if len(sys.argv) < 3:
            print("Error: run_custom requires at least review_id")
            print("Usage: main.py run_custom <review_id> [<user_id>] [<product_id>]")
            sys.exit(1)
        review_id = sys.argv[2]
        user_id = sys.argv[3] if len(sys.argv) > 3 else None
        product_id = sys.argv[4] if len(sys.argv) > 4 else None
        run_with_inputs(review_id, user_id, product_id)
    elif command == "train":
        train()
    elif command == "replay":
        replay()
    elif command == "test":
        test()
    else:
        print(f"Unknown command: {command}")
        sys.exit(1)
