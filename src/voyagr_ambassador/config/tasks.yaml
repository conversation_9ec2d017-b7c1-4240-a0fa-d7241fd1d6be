retrieve_data_task:
  description: >
    Retrieve comprehensive data from the ZEP knowledge graph using the provided review_id: {review_id}.
    Extract all relevant information about the user who wrote the review, the Expert Advisor (EA) product
    being reviewed, and the review content itself. Focus on gathering trading patterns, user preferences,
    product performance metrics, and sentiment indicators that will inform the outreach strategy.

    Key data points to retrieve:
    - User profile: trading experience, risk tolerance, preferred strategies, communication patterns
    - Product details: EA performance, features, market fit, pricing, user feedback trends
    - Review context: sentiment, specific pain points, satisfaction levels, technical feedback

    Ensure all data is properly structured and validated before passing to downstream agents.
  expected_output: >
    A comprehensive DataRetrievalOutput JSON structure containing:
    - user_data: Complete user profile with trading characteristics and communication preferences
    - product_data: Detailed EA information including performance metrics and feature analysis
    - review_data: Full review analysis with sentiment, key themes, and actionable insights
    - metadata: Data quality indicators, retrieval timestamp, and confidence scores
  async_execution: false
  agent: Data-Retriever
  output_json: DataRetrievalOutput
enrich_user_profile_task:
  description: >
    Analyze and enrich the trader's profile using the comprehensive data retrieved for review_id: {review_id}.
    Transform raw user data into actionable insights about trading behavior, communication preferences,
    and psychological profile. Focus on identifying key characteristics that will inform personalized
    outreach strategies.

    Analysis should include:
    - Trading experience level and sophistication
    - Risk tolerance and preferred trading strategies
    - Communication style and language preferences
    - Engagement patterns and response likelihood
    - Pain points and unmet needs in current trading setup
    - Cultural and regional considerations for messaging

    Use advanced profiling techniques to create a comprehensive psychological and behavioral model
    that enables highly personalized and effective outreach messaging.
  expected_output: >
    A detailed UserProfilerOutput JSON structure containing:
    - trading_profile: Experience level, risk tolerance, preferred strategies, account characteristics
    - communication_profile: Language, formality preferences, response patterns, engagement level
    - psychological_profile: Personality traits, decision-making style, motivation factors
    - pain_points: Current challenges, frustrations, and unmet needs
    - opportunity_indicators: Signals for product fit and engagement potential
    - personalization_data: Cultural context, timing preferences, and messaging guidelines
  async_execution: false
  agent: User-Profiler
  context:
  - retrieve_data_task
  output_json: UserProfilerOutput
analyze_product_task:
  description: >
    Conduct comprehensive analysis of the Expert Advisor (EA) product associated with review_id: {review_id}.
    Evaluate product strengths, weaknesses, market positioning, and fit signals based on retrieved data.
    Focus on identifying unique selling propositions, competitive advantages, and potential objections
    that need to be addressed in outreach messaging.

    Analysis should cover:
    - Product performance metrics and backtesting results
    - Feature differentiation and competitive positioning
    - User feedback patterns and satisfaction indicators
    - Market fit signals and target audience alignment
    - Pricing strategy and value proposition strength
    - Technical requirements and compatibility factors
    - Risk factors and potential user concerns

    Synthesize findings into actionable insights that inform conversation strategy and messaging approach.
  expected_output: >
    A comprehensive ProductAnalystOutput JSON structure containing:
    - product_overview: Core features, performance metrics, and technical specifications
    - market_analysis: Competitive positioning, target market fit, and differentiation factors
    - strength_analysis: Key advantages, unique features, and compelling value propositions
    - weakness_analysis: Potential concerns, limitations, and competitive disadvantages
    - user_feedback_summary: Sentiment trends, common praise points, and frequent complaints
    - recommendation_strategy: Positioning approach, key selling points, and objection handling
    - fit_indicators: Signals for user-product compatibility and engagement potential
  async_execution: false
  agent: Product-Analyst
  context:
  - retrieve_data_task
  output_json: ProductAnalystOutput
design_conversation_strategy_task:
  description: >
    Design a comprehensive conversation strategy for engaging the user associated with review_id: {review_id}.
    Synthesize insights from user profiling and product analysis to create a personalized outreach approach
    that maximizes engagement probability while maintaining compliance with MQL5 community guidelines.

    Strategy development should include:
    - Primary conversation intent and secondary objectives
    - Personalized approach rationale based on user psychology
    - Key talking points that resonate with user interests
    - Topics and approaches to avoid based on user profile
    - Message sequence planning (ice-breaker, follow-up, CTA)
    - Tone and formality requirements for optimal reception
    - Cultural and linguistic considerations
    - Compliance guardrails and risk mitigation
    - Success metrics and escalation pathways

    Ensure the strategy aligns user needs with business objectives while respecting user preferences
    and community standards.
  expected_output: >
    A detailed StrategyDesignerOutput JSON structure containing:
    - conversation_strategy: Primary intent, approach rationale, key talking points, topics to avoid
    - message_sequence: Ice-breaker focus, follow-up strategy, call-to-action type, escalation path
    - guard_rails: Tone requirements, formality level, cultural considerations, compliance notes
    - personalization_elements: User-specific customization points and messaging hooks
    - expected_outcome: Success criteria and desired user response
    - strategy_confidence: Confidence score for the recommended approach
  async_execution: false
  agent: Strategy-Designer
  context:
  - enrich_user_profile_task
  - analyze_product_task
  output_json: StrategyDesignerOutput
create_message_task:
  description: >
    Create compelling, personalized outreach messages for the user associated with review_id: {review_id}.
    Transform the conversation strategy into actual message content that is engaging, compliant, and
    optimized for the user's communication preferences and cultural context.

    Message creation should incorporate:
    - User's preferred language and formality level
    - Personalized hooks based on trading profile and interests
    - Strategic talking points from the conversation plan
    - Compliance with MQL5 community guidelines and anti-spam policies
    - Cultural sensitivity and regional communication norms
    - Clear value proposition and compelling call-to-action
    - Professional tone that builds trust and credibility
    - Optimal message length and structure for platform constraints

    Generate multiple message variants for A/B testing and follow-up sequences.
    Ensure all messages pass compliance checks and maintain high engagement potential.
  expected_output: >
    A comprehensive CopyWriterOutput JSON structure containing:
    - primary_messages: Main outreach messages optimized for initial contact
    - follow_up_messages: Sequence of follow-up messages for non-responders
    - alternative_versions: A/B test variants with different approaches or tones
    - compliance_analysis: Compliance scores, risk assessment, and policy adherence
    - personalization_summary: Applied personalization elements and customization rationale
    - message_metadata: Character counts, readability scores, and optimization metrics
    - recommended_timing: Optimal send times based on user profile and engagement patterns
  async_execution: false
  agent: Copy-Writer
  context:
  - design_conversation_strategy_task
  output_json: CopyWriterOutput
